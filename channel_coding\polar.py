import numpy as np

def get_polar_generator_matrix(n):
    """
    生成 Polar 码的生成矩阵 G_N = F_2^{\otimes n}
    其中 F_2 = [[1, 0], [1, 1]]
    N = 2^n
    """
    F2 = np.array([[1, 0], [1, 1]], dtype=int)
    G = F2.copy()
    if n == 1:
        return G
    for _ in range(1, n):
        G = np.kron(G, F2)
    return G

def get_polar_generator_matrix_recursive(n_log2):
    """
    递归生成 Polar 码的生成矩阵 G_N = B_N * (F_2^{\otimes n_log2})
    其中 B_N 是比特反转置换矩阵。
    这种方法更接近 Arıkan 论文中的定义，但直接使用 F_2^{\otimes n} 也很常见。
    为了简化，我们这里不显式构造 B_N，而是通常在编码/解码算法中隐式处理比特反转。
    此函数仍返回 F_2^{\otimes n_log2}。
    """
    N = 1 << n_log2
    G = np.zeros((N, N), dtype=int)
    # 初始化 G_1 (即 F_2 for n=1, N=2)
    if n_log2 == 0: # N=1
        return np.array([[1]], dtype=int)
    if n_log2 == 1: # N=2
        return np.array([[1,0],[1,1]], dtype=int)

    # 递归构造
    # G_N = [[G_{N/2}, 0_{N/2}], [G_{N/2}, G_{N/2}]] # 这是另一种理解，但与kronecker积等价
    # 这里我们坚持使用kronecker积定义
    F2 = np.array([[1, 0], [1, 1]], dtype=int)
    G_prev = F2
    for _ in range(1, n_log2):
        G_prev = np.kron(G_prev, F2)
    return G_prev


def polar_encode_matrix_multiplication(information_bits, n_log2, frozen_indices):
    """
    使用生成矩阵进行 Polar 编码 (模2加法)

    参数:
    information_bits (numpy.ndarray): 包含 K 个信息比特的数组 (0或1)
    n_log2 (int): log2(N)，其中 N 是码长
    frozen_indices (numpy.ndarray): 包含 N-K 个冻结位索引的数组

    返回:
    numpy.ndarray: 编码后的码字，长度为 N
    """
    N = 1 << n_log2
    K = len(information_bits)

    if N - K != len(frozen_indices):
        raise ValueError("信息比特数和冻结比特数之和必须等于码长 N")

    u = np.zeros(N, dtype=int)
    info_idx_ptr = 0
    for i in range(N):
        if i not in frozen_indices:
            if info_idx_ptr < K:
                u[i] = information_bits[info_idx_ptr]
                info_idx_ptr += 1
            else:
                # 通常不应该发生，意味着frozen_indices的设置有问题或K值不对
                raise ValueError("信息比特数量与可用信道不匹配")
        # else: u[i] 默认为 0 (冻结比特)

    # G_N = get_polar_generator_matrix(n_log2) # 使用kronecker积构造
    # 或者使用 Arıkan 论文中更常见的递归结构，但通常直接用 G_N = F_2^{\otimes n}
    # 为了简化，这里我们直接用 F_2^{\otimes n}

    # 实际编码中，更高效的是递归实现，而非显式矩阵乘法
    # x = u @ G_N % 2 # 进行模2矩阵乘法

    # 使用递归编码实现 (更高效)
    codeword = polar_encode_recursive(u)

    return codeword

def polar_encode_recursive(u_in):
    """
    递归方式实现 Polar 编码 x = u * G_N, G_N = F_2^{\otimes n}
    这里的乘法是模2的。
    输入 u_in 已经是包含了信息位和冻结位的完整向量。
    """
    N = len(u_in)
    if N == 1:
        return u_in.copy() # 对于单个元素，编码结果就是它自身

    # 将输入 u 分成两半
    u1 = u_in[0:N//2]
    u2 = u_in[N//2:N]

    # 递归编码
    # 根据 F_2 的结构:
    # x_upper = u_upper + u_lower
    # x_lower = u_lower
    # 然后这两部分分别再进行 N/2 的 Polar 编码
    # x = [encode(u1+u2), encode(u2)] (这里的 '+' 是模2加)

    temp1 = (u1 + u2) % 2 # u_upper XOR u_lower
    temp2 = u2.copy()       # u_lower

    x1 = polar_encode_recursive(temp1)
    x2 = polar_encode_recursive(temp2)

    return np.concatenate((x1, x2))


def get_channel_reliability_indices(N, design_snr_db=0):
    """
    一个简化的获取信道可靠性顺序的函数 (仅为示例)。
    实际应用中会使用更复杂的方法如巴氏参数、密度进化或高斯近似。
    这里我们用一个非常简化的假设：索引的比特反转顺序的自然序，
    或者直接返回一个固定的顺序（不准确，仅为演示）。
    更准确的方法需要信道参数。

    为了演示，我们这里仅返回一个固定的、通常认为可靠性较高的索引子集。
    在实际的Polar码设计中，这一步至关重要。
    """
    # 这是一个非常粗略的示例，并不代表真实的可靠性排序
    # 真实的可靠性排序依赖于信道参数（如设计SNR）
    # 对于AWGN信道，可以使用高斯近似等方法
    # 这里我们只是简单地选择一些索引

    # 假设：高位索引（经过比特反转后）更可靠，这并不总是成立，但作为一个简单占位符
    # 更好的方法是预计算可靠性序列，例如从文献或标准中获取
    # 或者实现一个可靠性估计算法

    # 示例：Arıkan 的原始论文中，对于 BEC(0.5)，N=8 时，可靠性从高到低为 {7,6,5,3}
    # 这里我们不能直接硬编码，因为 N 会变

    # 返回一个从 N-1 到 0 的降序索引，这仅是一个占位符，不代表真实可靠性
    # reliable_indices = np.arange(N - 1, -1, -1)

    # 另一个更符合一般观察的简化方法（但仍不精确）：
    # 具有更多 '1' 的比特反转索引通常更可靠。
    # 例如，对于 N=8:
    # 0 (000) -> 0 (000)
    # 1 (001) -> 4 (100)
    # 2 (010) -> 2 (010)
    # 3 (011) -> 6 (110)
    # 4 (100) -> 1 (001)
    # 5 (101) -> 5 (101)
    # 6 (110) -> 3 (011)
    # 7 (111) -> 7 (111)
    # "好"信道往往是那些比特反转后索引值较大的。

    # 为了使代码能运行，我们返回一个排序后的索引列表。
    # 在实际应用中，你需要一个基于信道模型的可靠性计算函数。
    # 例如，对于AWGN信道，巴氏参数 Z(W_N^{(i)}) 可以递归计算：
    # Z(W_{2N}^{(2i-1)}) = Z(W_N^{(i)})^2
    # Z(W_{2N}^{(2i)}) = 2Z(W_N^{(i)}) - Z(W_N^{(i)})^2
    # 初始 Z(W) = exp(-SNR) for BEC, or approx for AWGN.
    # 可靠性越高的信道，Z 值越小。

    # 这是一个非常简化的示例，不用于实际系统！
    # 假设我们有一些预计算的可靠性（这里仅为占位符）
    if N == 8: # 示例来自某文献，针对特定信道
        # 可靠性从低到高排序的索引 (巴氏参数从大到小)
        # 假设这是针对特定设计SNR的索引顺序，值越小越不可靠
        # 我们需要的是可靠性从高到低的索引
        # 假设：索引 7,6,5,3 是可靠的 (信息位)，0,1,2,4 是冻结位
        # 这个顺序是 specific 的，我们这里只提供一个通用占位
        pass

    # 作为一个非常通用的占位符，我们返回自然顺序的索引，
    # 然后用户需要自己选择哪些是 "好" 的。
    # 实际上，可靠性排序是 Polar 码设计的核心之一。
    # 这里我们返回一个随机打乱的索引，以强调用户需要自己提供可靠的索引。
    # np.random.seed(0) # for reproducibility
    # indices = np.arange(N)
    # np.random.shuffle(indices)
    # return indices

    # 更为结构化的简化占位符：比特反转索引的自然序
    # 这也并不直接对应可靠性，但提供了一个确定的顺序
    n = int(np.log2(N))
    indices = np.arange(N)
    bit_reversed_indices = np.array([int(bin(i)[2:].zfill(n)[::-1], 2) for i in indices])
    # 通常，比特反转索引值大的信道更可靠 (这是一个普遍趋势，但非绝对)
    # 所以我们按比特反转索引的降序排列原始索引
    sorted_original_indices_by_reversed_reliability = indices[np.argsort(-bit_reversed_indices)]
    return sorted_original_indices_by_reversed_reliability


# --- 主程序示例 ---
if __name__ == "__main__":
    N_val = 8  # 码长 (必须是2的幂)
    K_val = 4  # 信息比特数

    if not (N_val > 0 and (N_val & (N_val - 1) == 0)): # 检查是否为2的幂
        raise ValueError("N 必须是2的幂")
    if K_val > N_val:
        raise ValueError("K 不能大于 N")

    n_log2_val = int(np.log2(N_val))

    # 1. 假设我们有一些信息比特
    my_information_bits = np.array([1, 0, 1, 1]) # 长度为 K_val
    if len(my_information_bits) != K_val:
        raise ValueError(f"需要 {K_val} 个信息比特")

    # 2. 确定信息位和冻结位的位置
    #    在实际应用中，这一步非常关键，需要根据信道质量来选择。
    #    可靠性高的信道承载信息位，可靠性低的作为冻结位。
    #    这里我们只是简单地选择一些索引作为示例。
    #    `get_channel_reliability_indices` 返回的是一个从最可靠到最不可靠的索引排序。
    #    所以我们选择前 K_val 个作为信息位索引。

    # 获取一个（简化的）信道可靠性排序，从最可靠到最不可靠
    # 注意：这个函数是高度简化的，实际应用需要精确的可靠性估计
    all_channel_indices_sorted_by_reliability = get_channel_reliability_indices(N_val)

    # 信息位放在最可靠的信道上
    information_indices = all_channel_indices_sorted_by_reliability[:K_val]
    # 冻结位放在最不可靠的信道上
    frozen_bit_indices = all_channel_indices_sorted_by_reliability[K_val:]

    print(f"码长 N: {N_val}")
    print(f"信息比特数 K: {K_val}")
    print(f"信息比特: {my_information_bits}")
    print(f"选择承载信息比特的信道索引 (可靠性高): {sorted(information_indices)}")
    print(f"选择承载冻结比特的信道索引 (可靠性低): {sorted(frozen_bit_indices)}")

    # 3. 构造输入向量 u
    u_vector = np.zeros(N_val, dtype=int) # 默认为冻结位 (0)
    info_ptr = 0
    for idx in range(N_val):
        if idx in information_indices: # 检查原始索引是否在信息索引集合中
            u_vector[idx] = my_information_bits[info_ptr]
            info_ptr +=1

    print(f"构造的输入向量 u (信息位 + 冻结位): {u_vector}")

    # 4.进行 Polar 编码
    # (方法1: 使用显式生成矩阵 - 不推荐用于大 N)
    # G_N_matrix = get_polar_generator_matrix(n_log2_val)
    # codeword_matrix_mult = u_vector @ G_N_matrix % 2
    # print(f"编码后的码字 (矩阵乘法): {codeword_matrix_mult}")

    # (方法2: 使用递归编码 - 更高效)
    codeword_recursive = polar_encode_recursive(u_vector)
    print(f"编码后的码字 (递归方法): {codeword_recursive}")

    # (方法3: 使用封装好的函数，它内部使用递归编码)
    # 为了使用 polar_encode_matrix_multiplication (其内部已改为递归)，
    # 我们需要传入 frozen_indices 的列表。
    # frozen_indices_list = frozen_bit_indices # 已经是正确的了
    # codeword_unified = polar_encode_matrix_multiplication(my_information_bits, n_log2_val, frozen_indices_list)
    # print(f"编码后的码字 (统一接口): {codeword_unified}")
    # 验证一下 u_vector 是否与 polar_encode_matrix_multiplication 内部构造的一致
    # 注意：polar_encode_matrix_multiplication 的输入是 information_bits 和 frozen_indices，
    # 它会在内部构造 u。我们这里是先构造了 u，再调用递归编码。
    # 两种方式应该得到相同的结果，只要 u 的构造逻辑一致。


    # 验证生成矩阵 (可选)
    if N_val <= 8: # 只对小N打印，避免过大输出
        G_N_matrix_for_verification = get_polar_generator_matrix_recursive(n_log2_val)
        print(f"\n生成矩阵 G_{N_val} (F_2^{{\otimes {n_log2_val}}}):")
        print(G_N_matrix_for_verification)
        # 通过 G_N u^T (模2) 来验证递归编码
        # 注意：Python中 @ 表示矩阵乘法，但我们需要将 u_vector 视为行向量，
        # 或者 G_N.T @ u_vector.T
        # x = u G_N
        manual_encode = u_vector @ G_N_matrix_for_verification % 2
        print(f"手动通过 u @ G_N 计算的码字: {manual_encode}")
        assert np.array_equal(codeword_recursive, manual_encode), "递归编码与矩阵乘法结果不一致"
