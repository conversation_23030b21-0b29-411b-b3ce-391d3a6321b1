#!/usr/bin/env python3
"""
LDPC调试脚本 - 诊断编解码问题
"""

import numpy as np
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ldpc import *

def debug_encoding():
    """调试编码过程"""
    print("=== 编码调试 ===\n")
    
    # 使用最简单的校验矩阵
    H = np.array([
        [1, 1, 0, 1, 0, 0],
        [0, 1, 1, 0, 1, 0],
        [1, 0, 1, 0, 0, 1]
    ])
    
    print("校验矩阵 H:")
    print(H)
    print()
    
    encoder = LDPCEncoder(H)
    print("生成矩阵 G:")
    print(encoder.G)
    print()
    
    # 验证 H * G^T = 0 (mod 2)
    HG_T = np.dot(H, encoder.G.T) % 2
    print("H * G^T (mod 2):")
    print(HG_T)
    print(f"是否为零矩阵: {np.all(HG_T == 0)}")
    print()
    
    # 测试编码
    info_bits = np.array([1, 0, 1])
    codeword = encoder.encode(info_bits)
    
    print(f"信息位: {info_bits}")
    print(f"码字: {codeword}")
    
    # 手动验证编码
    manual_codeword = np.dot(info_bits, encoder.G) % 2
    print(f"手动编码: {manual_codeword}")
    print(f"编码一致: {np.array_equal(codeword, manual_codeword)}")
    print()
    
    # 验证校验子
    syndrome = np.dot(H, codeword) % 2
    print(f"校验子: {syndrome}")
    print(f"校验子为零: {np.all(syndrome == 0)}")
    print()

def debug_decoding():
    """调试解码过程"""
    print("=== 解码调试 ===\n")
    
    H = np.array([
        [1, 1, 0, 1, 0, 0],
        [0, 1, 1, 0, 1, 0],
        [1, 0, 1, 0, 0, 1]
    ])
    
    encoder = LDPCEncoder(H)
    decoder = LDPCDecoder(H, max_iterations=10)
    
    # 测试全零码字
    print("测试全零码字:")
    zero_codeword = np.array([0, 0, 0, 0, 0, 0])
    zero_symbols = bits_to_bpsk(zero_codeword)
    zero_llr = bpsk_to_llr(zero_symbols, 0.01)
    
    print(f"全零码字: {zero_codeword}")
    print(f"BPSK符号: {zero_symbols}")
    print(f"LLR: {zero_llr}")
    
    decoded_zero, _, _ = decoder.decode(zero_llr, verbose=True)
    print(f"解码结果: {decoded_zero}")
    print(f"解码正确: {np.array_equal(zero_codeword, decoded_zero)}")
    print()
    
    # 测试全一码字（如果是有效码字）
    print("测试其他码字:")
    for i in range(8):  # 测试所有3位信息位组合
        info_bits = np.array([(i >> j) & 1 for j in range(3)])
        codeword = encoder.encode(info_bits)
        
        # 检查是否为有效码字
        if decoder.is_valid_codeword(codeword):
            symbols = bits_to_bpsk(codeword)
            llr = bpsk_to_llr(symbols, 0.01)
            
            decoded_bits, converged, iterations = decoder.decode(llr)
            
            print(f"信息位 {info_bits} -> 码字 {codeword} -> 解码 {decoded_bits}")
            print(f"  解码信息位: {decoded_bits[:3]}")
            print(f"  正确性: {np.array_equal(info_bits, decoded_bits[:3])}")
            print()

def debug_llr_mapping():
    """调试LLR映射"""
    print("=== LLR映射调试 ===\n")
    
    # 测试BPSK映射和LLR计算
    test_bits = np.array([0, 1])
    symbols = bits_to_bpsk(test_bits)
    
    print(f"比特: {test_bits}")
    print(f"BPSK符号: {symbols}")
    print()
    
    # 无噪声LLR
    llr_clean = bpsk_to_llr(symbols, 0.01)
    print(f"无噪声LLR: {llr_clean}")
    
    # 硬判决
    decoded_clean = (llr_clean < 0).astype(int)
    print(f"硬判决结果: {decoded_clean}")
    print(f"解码正确: {np.array_equal(test_bits, decoded_clean)}")
    print()
    
    # 测试有噪声情况
    print("有噪声测试:")
    for snr_db in [10, 5, 2, 0]:
        received_signal, noise_var = awgn_channel(symbols, snr_db)
        llr_noisy = bpsk_to_llr(received_signal, noise_var)
        decoded_noisy = (llr_noisy < 0).astype(int)
        
        print(f"SNR={snr_db}dB: 接收={received_signal}, LLR={llr_noisy}, 解码={decoded_noisy}")

def test_simple_repetition_code():
    """测试简单的重复码"""
    print("=== 简单重复码测试 ===\n")
    
    # (3,1) 重复码的校验矩阵
    H = np.array([
        [1, 1, 0],
        [1, 0, 1]
    ])
    
    print("重复码校验矩阵:")
    print(H)
    print()
    
    encoder = LDPCEncoder(H)
    decoder = LDPCDecoder(H)
    
    print("生成矩阵:")
    print(encoder.G)
    print()
    
    # 测试编码
    for bit in [0, 1]:
        info_bits = np.array([bit])
        codeword = encoder.encode(info_bits)
        
        print(f"信息位 [{bit}] -> 码字 {codeword}")
        
        # 验证码字
        is_valid = decoder.is_valid_codeword(codeword)
        print(f"  有效性: {is_valid}")
        
        if is_valid:
            # 无噪声解码
            symbols = bits_to_bpsk(codeword)
            llr = bpsk_to_llr(symbols, 0.01)
            decoded_bits, _, _ = decoder.decode(llr)
            
            print(f"  解码: {decoded_bits} -> 信息位 {decoded_bits[:1]}")
            print(f"  正确: {np.array_equal([bit], decoded_bits[:1])}")
        print()

def main():
    """主函数"""
    print("LDPC调试程序")
    print("=" * 50)
    print()
    
    debug_encoding()
    print("=" * 50 + "\n")
    
    debug_llr_mapping()
    print("=" * 50 + "\n")
    
    test_simple_repetition_code()
    print("=" * 50 + "\n")
    
    debug_decoding()

if __name__ == "__main__":
    main()
