#!/usr/bin/env python3
"""
LDPC编解码演示脚本

本脚本演示了LDPC码的编解码过程和性能评估
"""

import numpy as np
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ldpc import *

def demo_basic_encoding_decoding():
    """演示基本的LDPC编解码过程"""
    print("=== LDPC基本编解码演示 ===\n")

    # 创建一个简单的LDPC码
    H = np.array([
        [1, 1, 0, 1, 0, 0],
        [0, 1, 1, 0, 1, 0],
        [1, 0, 1, 0, 0, 1]
    ])

    print("校验矩阵 H:")
    print(H)
    print()

    # 创建编解码器
    encoder = LDPCEncoder(H)
    decoder = LDPCDecoder(H)

    # 显示码参数
    params = encoder.get_code_parameters()
    print(f"LDPC码参数:")
    print(f"  码长 n = {params['n']}")
    print(f"  信息位数 k = {params['k']}")
    print(f"  校验位数 m = {params['m']}")
    print(f"  码率 R = {params['rate']:.3f}")
    print()

    # 测试多个信息位序列
    test_sequences = [
        [0, 0, 0],
        [1, 0, 0],
        [0, 1, 0],
        [0, 0, 1],
        [1, 1, 0],
        [1, 0, 1],
        [0, 1, 1],
        [1, 1, 1]
    ]

    print("编解码测试:")
    print("信息位 -> 码字 -> 解码结果")
    print("-" * 35)

    for info_bits in test_sequences:
        # 编码
        codeword = encoder.encode(info_bits)

        # 无噪声解码
        symbols = bits_to_bpsk(codeword)
        llr = bpsk_to_llr(symbols, 0.001)  # 很小的噪声
        decoded_bits, _, _ = decoder.decode(llr)
        decoded_info = decoded_bits[:params['k']]

        # 验证码字
        is_valid = decoder.is_valid_codeword(codeword)

        print(f"{info_bits} -> {codeword.tolist()} -> {decoded_info.tolist()} (有效: {is_valid})")

    print()

def demo_performance_simulation():
    """演示LDPC性能仿真"""
    print("=== LDPC性能仿真演示 ===\n")

    # 使用示例LDPC码
    examples = create_example_ldpc_codes()
    H = examples['manual_6_3']['H']

    print("使用手工构造的LDPC码 (6,3)")
    print("进行性能仿真...")
    print()

    # 设置仿真参数
    snr_range = [0, 2, 4, 6, 8, 10]  # 信噪比范围
    num_frames = 1000  # 仿真帧数

    print(f"仿真参数:")
    print(f"  SNR范围: {snr_range} dB")
    print(f"  仿真帧数: {num_frames}")
    print()

    # 运行仿真
    snr_list, ber_list, fer_list = ldpc_simulation(
        H, snr_range, num_frames=num_frames, verbose=True
    )

    # 显示结果
    print("\n仿真结果:")
    print("SNR(dB) |   BER   |   FER")
    print("-" * 25)
    for i, snr in enumerate(snr_list):
        print(f"{snr:7.1f} | {ber_list[i]:.2e} | {fer_list[i]:.2e}")

    # 尝试绘制性能曲线
    try:
        plot_performance(snr_list, ber_list, fer_list,
                        title="LDPC (6,3) 性能曲线")
    except:
        print("\n注意: 无法绘制图形，可能是matplotlib未正确安装")

def demo_different_ldpc_codes():
    """演示不同的LDPC码"""
    print("=== 不同LDPC码演示 ===\n")

    examples = create_example_ldpc_codes()

    for name, info in examples.items():
        print(f"LDPC码: {info['description']}")
        H = info['H']

        # 创建编码器并显示参数
        encoder = LDPCEncoder(H)
        params = encoder.get_code_parameters()

        print(f"  参数: n={params['n']}, k={params['k']}, 码率={params['rate']:.3f}")
        print(f"  校验矩阵形状: {H.shape}")
        print(f"  校验矩阵密度: {np.sum(H) / (H.shape[0] * H.shape[1]):.3f}")
        print()

def demo_noise_effects():
    """演示噪声对解码的影响"""
    print("=== 噪声影响演示 ===\n")

    # 使用简单的LDPC码
    H = np.array([
        [1, 1, 0, 1, 0, 0],
        [0, 1, 1, 0, 1, 0],
        [1, 0, 1, 0, 0, 1]
    ])

    encoder = LDPCEncoder(H)
    decoder = LDPCDecoder(H)

    # 固定信息位
    info_bits = np.array([1, 0, 1])
    codeword = encoder.encode(info_bits)
    symbols = bits_to_bpsk(codeword)

    print(f"原始信息位: {info_bits}")
    print(f"编码后码字: {codeword}")
    print()

    # 测试不同SNR下的解码性能
    snr_values = [10, 5, 2, 1, 0, -2]

    print("不同SNR下的解码结果:")
    print("SNR(dB) | 解码信息位 | 迭代次数 | 收敛")
    print("-" * 45)

    for snr_db in snr_values:
        # 添加噪声
        received_signal, noise_var = awgn_channel(symbols, snr_db)
        llr = bpsk_to_llr(received_signal, noise_var)

        # 解码
        decoded_bits, converged, iterations = decoder.decode(llr)
        decoded_info = decoded_bits[:len(info_bits)]

        print(f"{snr_db:7.1f} | {str(decoded_info.tolist()):>11} | {iterations:>8} | {converged}")

def main():
    """主函数"""
    print("LDPC编解码演示程序")
    print("=" * 50)
    print()

    # 运行各种演示
    demo_basic_encoding_decoding()
    print("\n" + "=" * 50 + "\n")

    demo_different_ldpc_codes()
    print("=" * 50 + "\n")

    demo_noise_effects()
    print("\n" + "=" * 50 + "\n")

    demo_performance_simulation()

    print("\n" + "=" * 50)
    print("演示完成！")
    print("\n使用说明:")
    print("1. 可以通过修改校验矩阵H来测试不同的LDPC码")
    print("2. 调整SNR范围和仿真帧数来获得更精确的性能曲线")
    print("3. 尝试生成更大的LDPC码以获得更好的性能")

if __name__ == "__main__":
    main()
