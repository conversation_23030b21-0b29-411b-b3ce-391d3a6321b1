# LDPC编解码器

本目录包含了完整的LDPC (Low-Density Parity-Check) 编解码器实现，支持编码、解码和性能仿真。

## 文件说明

- `ldpc.py` - 主要的LDPC编解码器实现
- `ldpc_demo.py` - 演示脚本，展示各种功能
- `ldpc_test.py` - 测试脚本，用于验证功能
- `debug_ldpc.py` - 调试脚本，用于问题诊断
- `README.md` - 本说明文档

## 主要功能

### 1. LDPC编码器 (LDPCEncoder)
- 支持系统码和非系统码
- 基于校验矩阵的编码
- 自动生成生成矩阵

### 2. LDPC解码器 (LDPCDecoder)
- 基于置信传播算法的软判决解码
- 支持迭代解码和收敛检测
- 校验子检查功能

### 3. 校验矩阵生成
- 规则LDPC码生成
- 不规则LDPC码生成
- 手工构造的示例码

### 4. 性能仿真
- AWGN信道模拟
- BER/FER性能评估
- 可视化性能曲线

## 快速开始

### 基本使用

```python
from channel_coding.ldpc import *
import numpy as np

# 创建校验矩阵
H = np.array([
    [1, 1, 0, 1, 0, 0],
    [0, 1, 1, 0, 1, 0],
    [1, 0, 1, 0, 0, 1]
])

# 创建编解码器
encoder = LDPCEncoder(H)
decoder = LDPCDecoder(H)

# 编码
info_bits = np.array([1, 0, 1])
codeword = encoder.encode(info_bits)
print(f"信息位: {info_bits}")
print(f"码字: {codeword}")

# 解码
symbols = bits_to_bpsk(codeword)
llr = bpsk_to_llr(symbols, 0.01)
decoded_bits, converged, iterations = decoder.decode(llr)
decoded_info = decoded_bits[:len(info_bits)]
print(f"解码信息位: {decoded_info}")
```

### 性能仿真

```python
# 使用示例LDPC码
examples = create_example_ldpc_codes()
H = examples['manual_6_3']['H']

# 运行性能仿真
snr_range = range(0, 6)
snr_list, ber_list, fer_list = ldpc_simulation(
    H, snr_range, num_frames=100, verbose=True
)

# 绘制性能曲线
plot_performance(snr_list, ber_list, fer_list)
```

### 生成LDPC码

```python
# 生成规则LDPC码
H_regular = generate_regular_ldpc_matrix(n=15, k=11, dv=3, dc=11)

# 生成不规则LDPC码
var_dist = {2: 10, 3: 20}  # 变量节点度数分布
check_dist = {5: 12}       # 校验节点度数分布
H_irregular = generate_irregular_ldpc_matrix(
    n=30, k=18, 
    var_degree_dist=var_dist,
    check_degree_dist=check_dist
)
```

## 运行演示

```bash
# 基本测试
python ldpc.py

# 完整演示
python ldpc_demo.py

# 调试测试
python debug_ldpc.py
```

## 技术细节

### BPSK调制约定
- 比特0 → BPSK符号-1
- 比特1 → BPSK符号+1

### LLR约定
- LLR > 0 表示比特更可能是0
- LLR < 0 表示比特更可能是1

### 置信传播算法
- 变量节点更新：消息传递
- 校验节点更新：双曲正切函数
- 迭代直到收敛或达到最大迭代次数

## 性能特点

### 优点
- 接近香农极限的性能
- 并行解码能力
- 灵活的码率设计

### 适用场景
- 数字通信系统
- 存储系统
- 卫星通信
- 无线通信

## 参数说明

### 编码器参数
- `H`: 校验矩阵 (m×n)
- `systematic`: 是否使用系统编码

### 解码器参数
- `H`: 校验矩阵
- `max_iterations`: 最大迭代次数
- `convergence_threshold`: 收敛阈值

### 仿真参数
- `snr_range`: 信噪比范围 (dB)
- `num_frames`: 仿真帧数
- `frame_length`: 每帧长度

## 注意事项

1. 校验矩阵必须满足LDPC码的要求（低密度）
2. 对于大型LDPC码，解码可能需要较长时间
3. 性能与校验矩阵的设计密切相关
4. 建议使用较大的码长以获得更好的性能

## 扩展功能

可以进一步扩展的功能：
- 更多的校验矩阵构造算法
- 其他调制方式支持
- 更高效的解码算法
- GPU加速实现
- 更多的性能评估指标

## 参考资料

- Gallager, R. G. "Low-density parity-check codes"
- MacKay, D. J. C. "Information Theory, Inference, and Learning Algorithms"
- Richardson, T. J. and Urbanke, R. L. "Modern Coding Theory"
