"""
LDPC (Low-Density Parity-Check) 编解码器实现

本模块实现了LDPC码的编码和解码功能，包括：
- LDPC编码器：基于校验矩阵的系统编码
- LDPC解码器：基于置信传播算法的软判决解码
- 校验矩阵生成：支持规则和不规则LDPC码
- 性能评估：误码率测试和收敛性分析

作者：AI Assistant
日期：2024
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.sparse import csr_matrix
from scipy.sparse.linalg import spsolve
import warnings
warnings.filterwarnings('ignore')


class LDPCEncoder:
    """
    LDPC编码器类

    实现基于校验矩阵的LDPC编码功能，支持系统码和非系统码
    """

    def __init__(self, H, systematic=True):
        """
        初始化LDPC编码器

        参数:
            H: 校验矩阵 (m x n)，其中 m 是校验位数，n 是码字长度
            systematic: 是否使用系统编码，默认为True
        """
        self.H = np.array(H, dtype=int)
        self.m, self.n = self.H.shape  # m: 校验位数, n: 码字长度
        self.k = self.n - self.m       # k: 信息位数
        self.systematic = systematic

        if self.k <= 0:
            raise ValueError("校验矩阵维度错误：信息位数必须大于0")

        # 验证校验矩阵
        if not self._validate_parity_matrix():
            warnings.warn("校验矩阵可能不满足LDPC码的要求")

        # 生成生成矩阵
        self.G = self._generate_generator_matrix()

    def _validate_parity_matrix(self):
        """验证校验矩阵是否满足LDPC码的基本要求"""
        # 检查矩阵是否为二进制
        if not np.all((self.H == 0) | (self.H == 1)):
            return False

        # 检查行列是否线性无关（简单检查）
        rank = np.linalg.matrix_rank(self.H)
        if rank < self.m:
            warnings.warn(f"校验矩阵的秩({rank})小于行数({self.m})")

        return True

    def _generate_generator_matrix(self):
        """
        生成生成矩阵G

        对于系统码，G = [I_k | P]，其中I_k是k×k单位矩阵，P是校验部分
        """
        if not self.systematic:
            # 非系统码的生成矩阵生成（简化实现）
            return np.eye(self.k, self.n, dtype=int)

        # 对于系统LDPC码，使用简化方法
        # 直接从校验矩阵构造生成矩阵
        I_k = np.eye(self.k, dtype=int)

        # 简单方法：假设前k列对应信息位，后m列对应校验位
        # 对于手工构造的小型LDPC码，直接使用校验矩阵的前k列作为P^T
        if self.H.shape[1] >= self.k:
            P_T = self.H[:, :self.k]
            P = P_T.T % 2
        else:
            # 如果列数不够，使用零矩阵
            P = np.zeros((self.k, self.m), dtype=int)

        G = np.hstack([I_k, P])
        return G



    def encode(self, info_bits):
        """
        编码信息位

        参数:
            info_bits: 信息位序列，长度为k的numpy数组或列表

        返回:
            codeword: 编码后的码字，长度为n的numpy数组
        """
        info_bits = np.array(info_bits, dtype=int)

        if len(info_bits) != self.k:
            raise ValueError(f"信息位长度({len(info_bits)})与编码器参数不匹配(k={self.k})")

        # 编码：c = u * G (mod 2)
        codeword = np.dot(info_bits, self.G) % 2

        return codeword

    def get_code_parameters(self):
        """
        获取码的参数

        返回:
            dict: 包含码长n、信息位数k、校验位数m和码率的字典
        """
        return {
            'n': self.n,      # 码长
            'k': self.k,      # 信息位数
            'm': self.m,      # 校验位数
            'rate': self.k / self.n  # 码率
        }


class LDPCDecoder:
    """
    LDPC解码器类

    实现基于置信传播算法(Belief Propagation)的软判决解码
    """

    def __init__(self, H, max_iterations=50, convergence_threshold=1e-6):
        """
        初始化LDPC解码器

        参数:
            H: 校验矩阵 (m x n)
            max_iterations: 最大迭代次数
            convergence_threshold: 收敛阈值
        """
        self.H = np.array(H, dtype=int)
        self.m, self.n = self.H.shape
        self.max_iterations = max_iterations
        self.convergence_threshold = convergence_threshold

        # 预计算校验矩阵的连接关系
        self._precompute_connections()

    def _precompute_connections(self):
        """预计算变量节点和校验节点的连接关系"""
        # 变量节点到校验节点的连接
        self.var_to_check = []
        for j in range(self.n):
            self.var_to_check.append(np.where(self.H[:, j] == 1)[0])

        # 校验节点到变量节点的连接
        self.check_to_var = []
        for i in range(self.m):
            self.check_to_var.append(np.where(self.H[i, :] == 1)[0])

    def decode(self, received_llr, verbose=False):
        """
        解码接收到的对数似然比

        参数:
            received_llr: 接收到的对数似然比，长度为n的numpy数组
            verbose: 是否输出详细信息

        返回:
            decoded_bits: 解码后的比特序列
            converged: 是否收敛
            iterations: 实际迭代次数
        """
        received_llr = np.array(received_llr, dtype=float)

        if len(received_llr) != self.n:
            raise ValueError(f"接收LLR长度({len(received_llr)})与解码器参数不匹配(n={self.n})")

        # 初始化消息
        # var_to_check_msg[j][i] 表示变量节点j发送给校验节点i的消息
        var_to_check_msg = {}
        for j in range(self.n):
            var_to_check_msg[j] = {}
            for i in self.var_to_check[j]:
                var_to_check_msg[j][i] = received_llr[j]

        # check_to_var_msg[i][j] 表示校验节点i发送给变量节点j的消息
        check_to_var_msg = {}
        for i in range(self.m):
            check_to_var_msg[i] = {}
            for j in self.check_to_var[i]:
                check_to_var_msg[i][j] = 0.0

        # 迭代解码
        for iteration in range(self.max_iterations):
            # 保存上一次的消息用于收敛检查
            prev_var_msg = {j: dict(var_to_check_msg[j]) for j in range(self.n)}

            # 校验节点更新
            for i in range(self.m):
                connected_vars = self.check_to_var[i]
                for j in connected_vars:
                    # 计算除了变量j之外的所有变量的乘积
                    product = 1.0
                    for k in connected_vars:
                        if k != j:
                            product *= np.tanh(var_to_check_msg[k][i] / 2.0)

                    # 避免数值问题
                    product = np.clip(product, -0.999999, 0.999999)
                    check_to_var_msg[i][j] = 2.0 * np.arctanh(product)

            # 变量节点更新
            for j in range(self.n):
                connected_checks = self.var_to_check[j]
                for i in connected_checks:
                    # 计算除了校验i之外的所有校验消息的和
                    sum_msg = received_llr[j]
                    for k in connected_checks:
                        if k != i:
                            sum_msg += check_to_var_msg[k][j]

                    var_to_check_msg[j][i] = sum_msg

            # 检查收敛性
            converged = True
            for j in range(self.n):
                for i in self.var_to_check[j]:
                    if abs(var_to_check_msg[j][i] - prev_var_msg[j][i]) > self.convergence_threshold:
                        converged = False
                        break
                if not converged:
                    break

            if verbose and (iteration + 1) % 10 == 0:
                print(f"迭代 {iteration + 1}/{self.max_iterations}")

            if converged:
                if verbose:
                    print(f"在第 {iteration + 1} 次迭代后收敛")
                break

        # 计算最终的后验LLR并做硬判决
        posterior_llr = np.zeros(self.n)
        for j in range(self.n):
            posterior_llr[j] = received_llr[j]
            for i in self.var_to_check[j]:
                posterior_llr[j] += check_to_var_msg[i][j]

        # 硬判决：LLR > 0 表示比特为0，LLR < 0 表示比特为1
        decoded_bits = (posterior_llr < 0).astype(int)

        return decoded_bits, converged, iteration + 1

    def check_syndrome(self, codeword):
        """
        检查码字的校验子

        参数:
            codeword: 码字

        返回:
            syndrome: 校验子，全零表示无错误
        """
        codeword = np.array(codeword, dtype=int)
        syndrome = np.dot(self.H, codeword) % 2
        return syndrome

    def is_valid_codeword(self, codeword):
        """
        检查是否为有效码字

        参数:
            codeword: 待检查的码字

        返回:
            bool: 是否为有效码字
        """
        syndrome = self.check_syndrome(codeword)
        return np.all(syndrome == 0)


def generate_regular_ldpc_matrix(n, k, dv, dc):
    """
    生成规则LDPC校验矩阵

    参数:
        n: 码长
        k: 信息位数
        dv: 变量节点度数
        dc: 校验节点度数

    返回:
        H: 校验矩阵
    """
    m = n - k  # 校验位数

    # 检查参数一致性
    if n * dv != m * dc:
        raise ValueError("参数不一致：n*dv 必须等于 m*dc")

    # 创建度数序列
    var_degrees = [dv] * n
    check_degrees = [dc] * m

    # 使用边交换算法生成矩阵
    H = np.zeros((m, n), dtype=int)

    # 创建边列表
    edges = []
    var_id = 0
    for degree in var_degrees:
        for _ in range(degree):
            edges.append(var_id)
        var_id += 1

    # 随机打乱边
    np.random.shuffle(edges)

    # 分配边到校验节点
    edge_idx = 0
    for check_id in range(m):
        for _ in range(check_degrees[check_id]):
            if edge_idx < len(edges):
                var_id = edges[edge_idx]
                H[check_id, var_id] = 1
                edge_idx += 1

    return H


def generate_irregular_ldpc_matrix(n, k, var_degree_dist, check_degree_dist):
    """
    生成不规则LDPC校验矩阵

    参数:
        n: 码长
        k: 信息位数
        var_degree_dist: 变量节点度数分布 {度数: 节点数}
        check_degree_dist: 校验节点度数分布 {度数: 节点数}

    返回:
        H: 校验矩阵
    """
    m = n - k

    # 验证度数分布
    if sum(var_degree_dist.values()) != n:
        raise ValueError("变量节点度数分布的总数必须等于n")
    if sum(check_degree_dist.values()) != m:
        raise ValueError("校验节点度数分布的总数必须等于m")

    # 检查边数一致性
    total_var_edges = sum(degree * count for degree, count in var_degree_dist.items())
    total_check_edges = sum(degree * count for degree, count in check_degree_dist.items())

    if total_var_edges != total_check_edges:
        raise ValueError("变量节点和校验节点的总边数必须相等")

    H = np.zeros((m, n), dtype=int)

    # 创建变量节点度数序列
    var_degrees = []
    for degree, count in var_degree_dist.items():
        var_degrees.extend([degree] * count)

    # 创建校验节点度数序列
    check_degrees = []
    for degree, count in check_degree_dist.items():
        check_degrees.extend([degree] * count)

    # 创建边列表
    var_edges = []
    for var_id, degree in enumerate(var_degrees):
        var_edges.extend([var_id] * degree)

    check_edges = []
    for check_id, degree in enumerate(check_degrees):
        check_edges.extend([check_id] * degree)

    # 随机打乱
    np.random.shuffle(var_edges)
    np.random.shuffle(check_edges)

    # 连接边
    for var_id, check_id in zip(var_edges, check_edges):
        H[check_id, var_id] = 1

    return H


def awgn_channel(signal, snr_db):
    """
    AWGN信道模拟

    参数:
        signal: 输入信号 (BPSK: +1/-1)
        snr_db: 信噪比(dB)

    返回:
        received_signal: 接收信号
        noise_variance: 噪声方差
    """
    signal = np.array(signal, dtype=float)

    # 计算噪声方差
    snr_linear = 10 ** (snr_db / 10)
    signal_power = np.mean(signal ** 2)
    noise_variance = signal_power / snr_linear

    # 添加高斯噪声
    noise = np.random.normal(0, np.sqrt(noise_variance), len(signal))
    received_signal = signal + noise

    return received_signal, noise_variance


def bits_to_bpsk(bits):
    """
    将比特转换为BPSK符号

    参数:
        bits: 比特序列 (0/1)

    返回:
        symbols: BPSK符号 (+1/-1)
    """
    bits = np.array(bits, dtype=int)
    return 2 * bits - 1  # 0 -> -1, 1 -> +1


def bpsk_to_llr(received_signal, noise_variance):
    """
    将BPSK接收信号转换为对数似然比

    参数:
        received_signal: 接收信号 (BPSK: 0->-1, 1->+1)
        noise_variance: 噪声方差

    返回:
        llr: 对数似然比 (LLR > 0 表示比特为0，LLR < 0 表示比特为1)
    """
    received_signal = np.array(received_signal, dtype=float)
    # 对于BPSK: 0->-1, 1->+1
    # LLR = -2 * received_signal / noise_variance
    # 这样：接收到-1时LLR>0(倾向于比特0)，接收到+1时LLR<0(倾向于比特1)
    llr = -2 * received_signal / noise_variance
    return llr


def calculate_ber(original_bits, decoded_bits):
    """
    计算误码率

    参数:
        original_bits: 原始比特
        decoded_bits: 解码比特

    返回:
        ber: 误码率
    """
    original_bits = np.array(original_bits, dtype=int)
    decoded_bits = np.array(decoded_bits, dtype=int)

    if len(original_bits) != len(decoded_bits):
        raise ValueError("比特序列长度不匹配")

    errors = np.sum(original_bits != decoded_bits)
    ber = errors / len(original_bits)

    return ber


def ldpc_simulation(H, snr_range, num_frames=100, frame_length=None, verbose=True):
    """
    LDPC编解码性能仿真

    参数:
        H: 校验矩阵
        snr_range: 信噪比范围 (dB)
        num_frames: 仿真帧数
        frame_length: 每帧长度，如果为None则使用信息位长度
        verbose: 是否显示进度

    返回:
        snr_db_list: 信噪比列表
        ber_list: 误码率列表
        fer_list: 误帧率列表
    """
    # 创建编解码器
    encoder = LDPCEncoder(H)
    decoder = LDPCDecoder(H)

    code_params = encoder.get_code_parameters()
    k = code_params['k']

    if frame_length is None:
        frame_length = k

    snr_db_list = []
    ber_list = []
    fer_list = []

    for snr_db in snr_range:
        if verbose:
            print(f"\n仿真 SNR = {snr_db} dB")

        total_bits = 0
        total_errors = 0
        frame_errors = 0

        for frame in range(num_frames):
            if verbose and (frame + 1) % 20 == 0:
                print(f"  帧 {frame + 1}/{num_frames}")

            # 生成随机信息位
            info_bits = np.random.randint(0, 2, frame_length)

            # 编码
            codeword = encoder.encode(info_bits)

            # BPSK调制
            symbols = bits_to_bpsk(codeword)

            # 通过AWGN信道
            received_signal, noise_variance = awgn_channel(symbols, snr_db)

            # 计算LLR
            llr = bpsk_to_llr(received_signal, noise_variance)

            # 解码
            decoded_bits, converged, iterations = decoder.decode(llr)

            # 提取信息位（对于系统码）
            if encoder.systematic:
                decoded_info = decoded_bits[:frame_length]
            else:
                # 对于非系统码，需要更复杂的信息位提取
                decoded_info = decoded_bits[:frame_length]

            # 计算错误
            bit_errors = np.sum(info_bits != decoded_info)
            total_bits += frame_length
            total_errors += bit_errors

            if bit_errors > 0:
                frame_errors += 1

        # 计算误码率和误帧率
        ber = total_errors / total_bits if total_bits > 0 else 0
        fer = frame_errors / num_frames

        snr_db_list.append(snr_db)
        ber_list.append(ber)
        fer_list.append(fer)

        if verbose:
            print(f"  BER = {ber:.2e}, FER = {fer:.2e}")

    return snr_db_list, ber_list, fer_list


def plot_performance(snr_db_list, ber_list, fer_list=None, title="LDPC Performance"):
    """
    绘制LDPC性能曲线

    参数:
        snr_db_list: 信噪比列表
        ber_list: 误码率列表
        fer_list: 误帧率列表（可选）
        title: 图标题
    """
    try:
        import matplotlib.pyplot as plt

        plt.figure(figsize=(10, 6))

        # 绘制BER曲线
        plt.semilogy(snr_db_list, ber_list, 'b-o', label='BER', linewidth=2, markersize=6)

        # 绘制FER曲线（如果提供）
        if fer_list is not None:
            plt.semilogy(snr_db_list, fer_list, 'r-s', label='FER', linewidth=2, markersize=6)

        plt.xlabel('SNR (dB)')
        plt.ylabel('Error Rate')
        plt.title(title)
        plt.grid(True, alpha=0.3)
        plt.legend()
        plt.xlim(min(snr_db_list), max(snr_db_list))
        plt.ylim(1e-6, 1)

        plt.tight_layout()
        plt.show()

    except ImportError:
        print("matplotlib未安装，无法绘制图形")
        print("SNR (dB) | BER | FER")
        print("-" * 25)
        for i, snr in enumerate(snr_db_list):
            if fer_list is not None:
                print(f"{snr:8.1f} | {ber_list[i]:.2e} | {fer_list[i]:.2e}")
            else:
                print(f"{snr:8.1f} | {ber_list[i]:.2e}")


def create_example_ldpc_codes():
    """
    创建一些示例LDPC码

    返回:
        dict: 包含不同LDPC码的字典
    """
    examples = {}

    # 示例1：小型规则LDPC码 (15,11)
    try:
        H1 = generate_regular_ldpc_matrix(n=15, k=11, dv=3, dc=11)
        examples['regular_15_11'] = {
            'H': H1,
            'description': '规则LDPC码 (15,11), dv=3, dc=11'
        }
    except:
        pass

    # 示例2：中等规则LDPC码 (63,45)
    try:
        H2 = generate_regular_ldpc_matrix(n=63, k=45, dv=3, dc=10)
        examples['regular_63_45'] = {
            'H': H2,
            'description': '规则LDPC码 (63,45), dv=3, dc=10'
        }
    except:
        pass

    # 示例3：手工构造的小型LDPC码
    H3 = np.array([
        [1, 1, 0, 1, 0, 0],
        [0, 1, 1, 0, 1, 0],
        [1, 0, 1, 0, 0, 1]
    ])
    examples['manual_6_3'] = {
        'H': H3,
        'description': '手工构造LDPC码 (6,3)'
    }

    # 示例4：不规则LDPC码
    try:
        var_dist = {2: 10, 3: 20}  # 10个度数为2的节点，20个度数为3的节点
        check_dist = {5: 12}       # 12个度数为5的节点
        H4 = generate_irregular_ldpc_matrix(n=30, k=18,
                                          var_degree_dist=var_dist,
                                          check_degree_dist=check_dist)
        examples['irregular_30_18'] = {
            'H': H4,
            'description': '不规则LDPC码 (30,18)'
        }
    except:
        pass

    return examples


def test_ldpc_basic():
    """
    基本LDPC编解码测试
    """
    print("=== LDPC编解码基本测试 ===\n")

    # 使用手工构造的小型LDPC码进行测试
    H = np.array([
        [1, 1, 0, 1, 0, 0],
        [0, 1, 1, 0, 1, 0],
        [1, 0, 1, 0, 0, 1]
    ])

    print("校验矩阵 H:")
    print(H)
    print()

    # 创建编解码器
    encoder = LDPCEncoder(H)
    decoder = LDPCDecoder(H)

    # 显示码参数
    params = encoder.get_code_parameters()
    print(f"码参数: n={params['n']}, k={params['k']}, m={params['m']}, 码率={params['rate']:.3f}")
    print()

    # 测试编码
    info_bits = np.array([1, 0, 1])
    print(f"信息位: {info_bits}")

    codeword = encoder.encode(info_bits)
    print(f"编码后: {codeword}")

    # 验证码字
    is_valid = decoder.is_valid_codeword(codeword)
    print(f"码字有效性: {is_valid}")
    print()

    # 测试无噪声解码
    symbols = bits_to_bpsk(codeword)
    llr = bpsk_to_llr(symbols, 0.01)  # 很小的噪声方差

    decoded_bits, converged, iterations = decoder.decode(llr, verbose=True)
    print(f"解码结果: {decoded_bits}")
    print(f"收敛状态: {converged}, 迭代次数: {iterations}")

    # 提取信息位
    decoded_info = decoded_bits[:params['k']]
    print(f"解码信息位: {decoded_info}")

    # 计算误码率
    ber = calculate_ber(info_bits, decoded_info)
    print(f"误码率: {ber}")
    print()

    # 测试有噪声情况
    print("=== 有噪声测试 (SNR = 2 dB) ===")
    received_signal, noise_var = awgn_channel(symbols, snr_db=2)
    llr_noisy = bpsk_to_llr(received_signal, noise_var)

    decoded_bits_noisy, converged_noisy, iter_noisy = decoder.decode(llr_noisy, verbose=True)
    decoded_info_noisy = decoded_bits_noisy[:params['k']]

    print(f"噪声下解码信息位: {decoded_info_noisy}")
    ber_noisy = calculate_ber(info_bits, decoded_info_noisy)
    print(f"噪声下误码率: {ber_noisy}")


if __name__ == "__main__":
    # 运行基本测试
    # test_ldpc_basic()

    H = np.array([
        [1, 1, 1, 0],
        [1, 0, 0, 1]
    ])

    H = generate_regular_ldpc_matrix(8, 4, 2, 4)
    print(H)
    encoder = LDPCEncoder(H)
    print(encoder.G)
    encoded = encoder.encode([0,1,1,1])
    print(encoded)
    decoder = LDPCDecoder(H)
    syndrome = decoder.check_syndrome(encoded)
    print(syndrome)
    ans = decoder.decode(-encoded * 2 + 1, True)
    print(ans)
