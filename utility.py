import numpy as np
import matplotlib.pyplot as plt
from matplotlib_config import configure

CHINESE_FONT_AVAILABLE = configure()

def plot_constellation_map(constellation, title="Constellation Diagram", save=None):
    """
    Plot the constellation diagram.
    :param constellation: List of complex numbers representing the constellation points.
    :param title: Title of the plot.
    """
    plt.figure(figsize=(8, 8))
    plt.scatter([point.real for point in constellation], [point.imag for point in constellation], marker='o')
    plt.title(title)
    plt.xlabel('In-Phase (I)')
    plt.ylabel('Quadrature (Q)')
    plt.grid()
    plt.axis('equal')
    if save is not None:
        plt.savefig(save, dpi=300, bbox_inches="tight")
    plt.show()


def plot_signal(t, signal, title="Signal Waveform", save=None):
    """
    Plot the signal waveform.
    :param signal: List of complex numbers representing the signal.
    :param t: Time vector for the x-axis.
    :param title: Title of the plot.
    """
    plt.figure(figsize=(10, 5))
    plt.plot(t, np.real(signal), label='In-Phase (I)')
    plt.plot(t, np.imag(signal), label='Quadrature (Q)')
    plt.title(title)
    plt.xlabel('Time')
    plt.ylabel('Amplitude')
    plt.legend()
    plt.grid()
    if save is not None:
        plt.savefig(save, dpi=300, bbox_inches="tight")
    plt.show()


def plot_constellation(samples, title="Signal Constellation", save=None):
    """
    Plot the constellation of the sampled signal.
    :param samples: List of complex numbers representing the sampled signal.
    :param title: Title of the plot.
    """
    plt.figure(figsize=(8, 8))
    plt.scatter(np.real(samples), np.imag(samples), marker='o')
    plt.title(title)
    plt.xlabel('In-Phase (I)')
    plt.ylabel('Quadrature (Q)')
    plt.grid()
    plt.axis('equal')
    if save is not None:
        plt.savefig(save, dpi=300, bbox_inches="tight")
    plt.show()


def plot_sampled_signal(samples, title="Sampled Signal Waveform", save=None):
    """
    Plot the sampled signal waveform.
    :param samples: List of complex numbers representing the sampled signal.
    :param title: Title of the plot.
    """
    plt.figure(figsize=(10, 5))
    plt.plot(np.real(samples), label='In-Phase (I)')
    plt.plot(np.imag(samples), label='Quadrature (Q)')
    plt.title(title)
    plt.xlabel('Sample Index')
    plt.ylabel('Amplitude')
    plt.legend()
    plt.grid()
    if save is not None:
        plt.savefig(save, dpi=300, bbox_inches="tight")
    plt.show()


def plot_spectrum(signal, fs, xlim=None, title="Signal Spectrum", save=None):
    """
    Plot the spectrum of the signal.
    :param signal: List of complex numbers representing the signal.
    :param title: Title of the plot.
    """
    plt.figure(figsize=(10, 5))
    plt.magnitude_spectrum(signal, Fs=fs, scale='dB')
    plt.title(title)
    plt.xlabel('Frequency')
    plt.ylabel('Magnitude')
    if xlim is not None:
        plt.xlim(xlim)
    plt.ylim(bottom=-100, top=0)
    plt.grid()
    if save is not None:
        plt.savefig(save, dpi=300, bbox_inches="tight")
    plt.show()


def save_plot(filename):
    """
    Save the current plot to a file.
    :param filename: Name of the file to save the plot.
    """
    plt.savefig(filename)
    plt.close()


def save_signal(signal, filename):
    """
    Save the signal to a file.
    :param filename: Name of the file to save the signal.
    :param signal: List of complex numbers representing the signal.
    """
    np.save(filename, signal)


def load_signal(filename):
    """
    Load the signal from a file.
    :param filename: Name of the file to load the signal from.
    :return: List of complex numbers representing the signal.
    """
    return np.load(filename)
