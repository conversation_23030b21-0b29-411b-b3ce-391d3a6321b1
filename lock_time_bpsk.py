import numpy as np
import matplotlib.pyplot as plt
from matplotlib_config import configure

CHINESE_FONT_AVAILABLE = configure()

def rrc(t, rolloff_factor):
    h = np.zeros(len(t))
    for i in range(len(t)):
        if t[i] == 0:
            h[i] = 1 - rolloff_factor + (4 * rolloff_factor / np.pi)
        elif abs(t[i]) == 1 / (4 * rolloff_factor):
            h[i] = (rolloff_factor / np.sqrt(2)) * ((1 + 2 / np.pi) * np.sin(np.pi / (4 * rolloff_factor)) + (1 - 2 / np.pi) * np.cos(np.pi / (4 * rolloff_factor)))
        else:
            h[i] = (np.sin(np.pi * t[i] * (1 - rolloff_factor)) + 4 * rolloff_factor * t[i] * np.cos(np.pi * t[i] * (1 + rolloff_factor)))
            h[i] /= (np.pi * t[i] * (1 - (4 * rolloff_factor * t[i]) ** 2))
    return h


def generate_baseband(message, num_symbols, symbol_rate, time_granularity, filter_symbols, rolloff_factor):
    simulation_time = num_symbols / symbol_rate
    t = np.arange(0, simulation_time, time_granularity)

    impulse_series = np.zeros(len(t))
    for i in range(num_symbols):
        impulse_series[round(i / symbol_rate / time_granularity)] = message[i]

    # RRC filter
    filter_length = round(filter_symbols / symbol_rate / time_granularity)
    if filter_length % 2 == 0:
        filter_length += 1
    t_filter = np.arange(-(filter_length - 1) / 2, (filter_length + 1) / 2) * time_granularity * symbol_rate
    rrc_response = rrc(t_filter, rolloff_factor)

    baseband_signal = np.convolve(impulse_series, rrc_response)
    t = np.arange(0, len(baseband_signal) * time_granularity, time_granularity)
    return t, baseband_signal


def sample_and_filter(signal, num_symbols, symbol_rate, time_granularity, sample_offset, over_sampling_ratio, filter_symbols, rolloff_factor):
    sample_rate = symbol_rate * over_sampling_ratio
    num_samples = (num_symbols + filter_symbols - 1) * over_sampling_ratio
    sampled_signal = np.zeros(num_samples, dtype=complex)
    for i in range(num_samples):
        sampled_signal[i] = signal[round(((i / sample_rate) + (sample_offset / symbol_rate)) / time_granularity)]

    discrete_filter_length = round(filter_symbols * over_sampling_ratio)
    if discrete_filter_length % 2 == 0:
        discrete_filter_length += 1
    t_discrete_filter = np.arange(-(discrete_filter_length - 1) / 2, (discrete_filter_length + 1) / 2) / over_sampling_ratio
    discrete_rrc_response = rrc(t_discrete_filter, rolloff_factor) / over_sampling_ratio

    filtered_signal = np.convolve(sampled_signal, discrete_rrc_response)
    return filtered_signal


def filter(signal, symbol_rate, time_granularity, filter_symbols, rolloff_factor):
    filter_length = round(filter_symbols / symbol_rate / time_granularity)
    if filter_length % 2 == 0:
        filter_length += 1
    t_filter = np.arange(-(filter_length - 1) / 2, (filter_length + 1) / 2) * time_granularity * symbol_rate
    rrc_response = rrc(t_filter, rolloff_factor) * symbol_rate * time_granularity

    filtered_signal = np.convolve(signal, rrc_response, mode="same")
    return filtered_signal


def detect_signal(signal, energy_threshold, consecutive_threshold):
    signal_energy = np.abs(signal) ** 2
    signal_detected = signal_energy > energy_threshold
    consecutive_count = 0
    for i in range(len(signal_detected)):
        if signal_detected[i]:
            consecutive_count += 1
            if consecutive_count >= consecutive_threshold:
                return i + 1
        else:
            consecutive_count = 0
    return -1


def sync_carrier_monotone(signal, loop_bandwidth, damping_factor, VCO_gain, init_freq=0):
    length = len(signal)
    freq_synced_signal = np.zeros(length, dtype=complex)
    phase_error = np.zeros(length)
    freq = np.zeros(length)

    natural_freq = 8 * damping_factor * loop_bandwidth / (4 * (damping_factor ** 2) + 1)
    denominator = 4 + 4 * damping_factor * natural_freq + (natural_freq ** 2)
    C1 = 8 * damping_factor * natural_freq / denominator
    C2 = 4 * (natural_freq ** 2) / denominator

    phase = 0
    if init_freq != 0:
        phase = np.angle(signal[0])
    loop_filter_state = init_freq
    for i in range(length):
        freq_synced_signal[i] = signal[i] * np.exp(-1j * phase)
        phase_error[i] = np.angle(freq_synced_signal[i])
        loop_filter_state = loop_filter_state + C2 * phase_error[i]
        loop_filter_out = C1 * phase_error[i] + loop_filter_state
        phase = phase + VCO_gain * loop_filter_out
        freq[i] = VCO_gain * loop_filter_out / 2 / np.pi

    return freq_synced_signal, phase_error, freq


def sync_clock(signal, num_symbols, over_sampling_ratio, filter_symbols):
    sync_offset = filter_symbols * over_sampling_ratio
    clock_synced_signal = np.zeros(num_symbols, dtype=complex)
    for i in range(num_symbols):
        clock_synced_signal[i] = signal[i * over_sampling_ratio + sync_offset]
    return clock_synced_signal


def sync_clock_true(signal):
    yi = 0
    yq = 0
    K = 0.5
    clock_offset = np.zeros(len(signal) // 4)
    for i in range(len(clock_offset)):
        yi = K * yi + np.abs(signal[4 * i])**2 - np.abs(signal[4 * i + 2])**2
        yq = K * yq + np.abs(signal[4 * i + 1])**2 - np.abs(signal[4 * i + 3])**2
        clock_offset[i] = np.arctan2(yq, yi) / 2 / np.pi
        if clock_offset[i] < 0:
            clock_offset[i] += 1
        clock_offset[i] = clock_offset[i] * 4
        if i != 0 and clock_offset[i] > 3.5 and clock_offset[i-1] < 0.5:
            clock_offset[i] -= 4
        elif i != 0 and clock_offset[i] < 0.5 and clock_offset[i-1] > 3.5:
            clock_offset[i] += 4
    return clock_offset


def sync_carrier_bpsk(signal, loop_bandwidth, damping_factor, VCO_gain, init_freq=0):
    length = len(signal)
    freq_synced_signal = np.zeros(length, dtype=complex)
    phase_error = np.zeros(length)
    freq = np.zeros(length)

    natural_freq = 8 * damping_factor * loop_bandwidth / (4 * (damping_factor ** 2) + 1)
    denominator = 4 + 4 * damping_factor * natural_freq + (natural_freq ** 2)
    C1 = 8 * damping_factor * natural_freq / denominator
    C2 = 4 * (natural_freq ** 2) / denominator

    phase = 0
    if init_freq != 0:
        phase = np.angle(signal[0])
    loop_filter_state = init_freq
    for i in range(length):
        freq_synced_signal[i] = signal[i] * np.exp(-1j * phase)
        # phase_error[i] = np.real(freq_synced_signal[i]) * np.imag(freq_synced_signal[i])
        phase_error[i] = np.angle(np.sign(np.real(freq_synced_signal[i])) * freq_synced_signal[i])
        loop_filter_state = loop_filter_state + C2 * phase_error[i]
        loop_filter_out = C1 * phase_error[i] + loop_filter_state
        phase = phase + VCO_gain * loop_filter_out
        freq[i] = VCO_gain * loop_filter_out / 2 / np.pi

    return freq_synced_signal, phase_error, freq


def plot_sync_freq(freq_synced_signal, phase_error, freq):
    plt.figure(figsize=(5, 5))
    plt.scatter(np.real(freq_synced_signal), np.imag(freq_synced_signal))
    plt.title("Constellation Diagram")
    plt.xlabel("In-Phase")
    plt.ylabel("Quadrature")
    plt.grid()

    plt.figure(figsize=(10, 5))
    plt.plot(phase_error)
    plt.title("Phase in Carrier Synchronization")
    plt.xlabel("Sample Index")
    plt.ylabel("Phase (radians)")
    plt.grid()

    plt.figure(figsize=(10, 5))
    plt.plot(freq)
    plt.title("Frequency in Carrier Synchronization")
    plt.xlabel("Sample Index")
    plt.ylabel("Frequency (Hz)")
    plt.grid()

    plt.show()


def lock_time_monotone():
    bandwidth = 1e6 # Hz
    rolloff_factor = 0.35
    symbol_rate = 2 * bandwidth / (1 + rolloff_factor)
    time_granularity = 1e-9 # seconds
    num_symbols = 100 # Number of symbols to simulate
    filter_symbols = 7 # for RRC filter

    message = np.ones(num_symbols)
    t, baseband_signal = generate_baseband(message, num_symbols, symbol_rate, time_granularity, filter_symbols, rolloff_factor)

    # freq_offset = 43600 # Hz
    freq_offset_list = np.arange(0, 87540, 100) # Hz
    phase_offset = 0 # radians
    snr_db_list = np.arange(15, 41, 5)

    lock_time_list = np.zeros((len(snr_db_list), len(freq_offset_list)))
    for snr_db in snr_db_list:
        noise_std = 1 / (10 ** (snr_db / 20))
        noise_std = noise_std / np.sqrt(2)

        for freq_offset in freq_offset_list:

            received_signal = baseband_signal * np.exp(1j * (2 * np.pi * freq_offset * t + phase_offset))
            received_signal = received_signal + np.random.normal(0, noise_std, len(baseband_signal))
            received_signal = received_signal + 1j * np.random.normal(0, noise_std, len(baseband_signal))

            over_sampling_ratio = 4 # sps, samples per symbol
            sample_offset = 0 # per symbol, from 0 to 1/sps

            sampled_signal = sample_and_filter(received_signal, num_symbols, symbol_rate, time_granularity, sample_offset, over_sampling_ratio, filter_symbols, rolloff_factor)

            energy_threshold = 0.1
            consecutive_threshold = 8
            signal_start = detect_signal(sampled_signal, energy_threshold, consecutive_threshold)
            if signal_start == -1:
                print("Signal not detected")
                continue

            detected_signal = sampled_signal[signal_start+4:-2*filter_symbols * over_sampling_ratio]

            loop_bandwidth = 0.03 # Loop bandwidth (Normalized)
            damping_factor = 0.707 # Damping factor
            VCO_gain = 1 # VCO gain

            init_freq = 0
            freq_synced_signal, phase_error, freq = sync_carrier_monotone(detected_signal, loop_bandwidth, damping_factor, VCO_gain, init_freq)
            freq = freq * symbol_rate * over_sampling_ratio
            # if freq_offset > 80000:
            #     plot_sync_freq(freq_synced_signal, phase_error, freq)
            freq_requirement = 24e3 # Hz
            delta_freq = np.abs(freq - freq_offset)
            freq_satisfied = np.nonzero(delta_freq > freq_requirement)[0]
            if len(freq_satisfied) == 0:
                lock_sample = 0
            else:
                lock_sample = max(freq_satisfied)
            lock_time = lock_sample / symbol_rate / over_sampling_ratio
            lock_time_list[snr_db_list == snr_db, freq_offset_list == freq_offset] = lock_time

    plt.figure(figsize=(10, 5))
    for i, snr_db in enumerate(snr_db_list):
        plt.plot(freq_offset_list, lock_time_list[i, :] * 1e6, label=f"SNR = {snr_db} dB")
    plt.title("科斯塔斯环的锁定时间与载波频偏的关系（噪声带宽Bn=0.03，残余频偏小于24kHz）")
    plt.xlabel("载波频偏 (Hz)")
    plt.ylabel("锁定时间 (us)")
    plt.legend()
    plt.grid()

    plt.figure(figsize=(10, 5))
    for i, snr_db in enumerate(snr_db_list):
        plt.plot(freq_offset_list, lock_time_list[i, :] * symbol_rate * over_sampling_ratio, label=f"SNR = {snr_db} dB")
    plt.title("科斯塔斯环的锁定时间与载波频偏的关系（噪声带宽Bn=0.03，残余频偏小于24kHz）")
    plt.xlabel("载波频偏 (Hz)")
    plt.ylabel("锁定时间 (采样点数)")
    plt.grid()
    plt.legend()
    plt.savefig("lock_time_monotone.png", dpi=300, bbox_inches="tight")
    plt.show()


def lock_noise_monotone():
    bandwidth = 1e6 # Hz
    rolloff_factor = 0.35
    symbol_rate = 2 * bandwidth / (1 + rolloff_factor)
    time_granularity = 1e-9 # seconds
    num_symbols = 100 # Number of symbols to simulate
    filter_symbols = 7 # for RRC filter

    message = np.ones(num_symbols)
    t, baseband_signal = generate_baseband(message, num_symbols, symbol_rate, time_granularity, filter_symbols, rolloff_factor)

    # freq_offset = 43600 # Hz
    freq_offset_list = np.arange(0, 87200, 100) # Hz
    phase_offset = 0 # radians
    # noise_std = 0.01
    snr_db_list = np.arange(20, 41, 10)

    freq_flunctuation_list = np.zeros((len(snr_db_list), len(freq_offset_list)))
    phase_error_list = np.zeros((len(snr_db_list), len(freq_offset_list)))
    for snr_db in snr_db_list:
        noise_std = 1 / (10 ** (snr_db / 20))
        noise_std = noise_std / np.sqrt(2)
        for freq_offset in freq_offset_list:

            received_signal = baseband_signal * np.exp(1j * (2 * np.pi * freq_offset * t + phase_offset))
            received_signal = received_signal + np.random.normal(0, noise_std, len(baseband_signal))
            received_signal = received_signal + 1j * np.random.normal(0, noise_std, len(baseband_signal))

            over_sampling_ratio = 4 # sps, samples per symbol
            sample_offset = 0 # per symbol, from 0 to 1/sps

            sampled_signal = sample_and_filter(received_signal, num_symbols, symbol_rate, time_granularity, sample_offset, over_sampling_ratio, filter_symbols, rolloff_factor)

            energy_threshold = 0.1
            consecutive_threshold = 8
            signal_start = detect_signal(sampled_signal, energy_threshold, consecutive_threshold)
            if signal_start == -1:
                print("Signal not detected")
                continue

            detected_signal = sampled_signal[signal_start:-2*filter_symbols * over_sampling_ratio]

            loop_bandwidth = 0.063 # Loop bandwidth (Normalized)
            damping_factor = 0.707 # Damping factor
            VCO_gain = 1 # VCO gain

            init_freq = freq_offset / symbol_rate/ over_sampling_ratio * 2 * np.pi
            freq_synced_signal, phase_error, freq = sync_carrier_monotone(detected_signal, loop_bandwidth, damping_factor, VCO_gain, init_freq)
            freq = freq * symbol_rate * over_sampling_ratio
            # if freq_offset > 35000:
            #     plot_sync_freq(freq_synced_signal, phase_error, freq)
            delta_freq = np.abs(freq - freq_offset)
            freq_flunctuation_list[snr_db_list == snr_db, freq_offset_list == freq_offset] = np.max(delta_freq[50:])
            phase_error_list[snr_db_list == snr_db, freq_offset_list == freq_offset] = np.max(phase_error[50:])

    plt.figure(figsize=(10, 5))
    for i, snr_db in enumerate(snr_db_list):
        plt.plot(freq_offset_list, freq_flunctuation_list[i, :], label=f"SNR = {snr_db} dB")
    plt.title("Carrier Synchronization Frequency Flunctuation")
    plt.xlabel("Frequency Offset (Hz)")
    plt.ylabel("Frequency Flunctuation (Hz)")
    plt.grid()
    plt.legend()

    plt.figure(figsize=(10, 5))
    for i, snr_db in enumerate(snr_db_list):
        plt.plot(freq_offset_list, phase_error_list[i, :], label=f"SNR = {snr_db} dB")
    plt.title("Carrier Synchronization Phase Error")
    plt.xlabel("Frequency Offset (Hz)")
    plt.ylabel("Phase Error (radians)")
    plt.grid()
    plt.legend()
    plt.show()


def lock_noise_monotone2():
    bandwidth = 1e6 # Hz
    rolloff_factor = 0.35
    symbol_rate = 2 * bandwidth / (1 + rolloff_factor)
    time_granularity = 1e-9 # seconds
    num_symbols = 1000 # Number of symbols to simulate
    filter_symbols = 7 # for RRC filter

    message = np.ones(num_symbols)
    t, baseband_signal = generate_baseband(message, num_symbols, symbol_rate, time_granularity, filter_symbols, rolloff_factor)

    freq_offset = 43600 # Hz
    # freq_offset_list = np.arange(0, 87200, 100) # Hz
    phase_offset = 0 # radians
    # noise_std = 0.01
    snr_db_list = np.arange(15, 41, 5)
    loop_bw_list = np.arange(0.001, 0.1, 0.001)

    freq_flunctuation_list = np.zeros((len(snr_db_list), len(loop_bw_list)))
    phase_error_list = np.zeros((len(snr_db_list), len(loop_bw_list)))
    for snr_db in snr_db_list:
        noise_std = 1 / (10 ** (snr_db / 20))
        noise_std = noise_std / np.sqrt(2)
        for loop_bandwidth in loop_bw_list:

            received_signal = baseband_signal * np.exp(1j * (2 * np.pi * freq_offset * t + phase_offset))
            received_signal = received_signal + np.random.normal(0, noise_std, len(baseband_signal))
            received_signal = received_signal + 1j * np.random.normal(0, noise_std, len(baseband_signal))

            over_sampling_ratio = 4 # sps, samples per symbol
            sample_offset = 0 # per symbol, from 0 to 1/sps

            sampled_signal = sample_and_filter(received_signal, num_symbols, symbol_rate, time_granularity, sample_offset, over_sampling_ratio, filter_symbols, rolloff_factor)

            energy_threshold = 0.1
            consecutive_threshold = 8
            signal_start = detect_signal(sampled_signal, energy_threshold, consecutive_threshold)
            if signal_start == -1:
                print("Signal not detected")
                continue

            detected_signal = sampled_signal[signal_start:-2*filter_symbols * over_sampling_ratio]

            # loop_bandwidth = 0.063 # Loop bandwidth (Normalized)
            damping_factor = 0.707 # Damping factor
            VCO_gain = 1 # VCO gain

            init_freq = freq_offset / symbol_rate/ over_sampling_ratio * 2 * np.pi
            freq_synced_signal, phase_error, freq = sync_carrier_monotone(detected_signal, loop_bandwidth, damping_factor, VCO_gain, init_freq)
            # freq = freq * symbol_rate * over_sampling_ratio
            # if freq_offset > 35000:
            #     plot_sync_freq(freq_synced_signal, phase_error, freq)
            delta_freq = np.abs(freq - freq_offset / symbol_rate / over_sampling_ratio)
            freq_flunctuation_list[snr_db_list == snr_db, loop_bw_list == loop_bandwidth] = np.max(delta_freq[1000:])
            phase_error_list[snr_db_list == snr_db, loop_bw_list == loop_bandwidth] = np.max(phase_error[1000:])

    plt.figure(figsize=(10, 5))
    for i, snr_db in enumerate(snr_db_list):
        plt.plot(loop_bw_list, freq_flunctuation_list[i, :], label=f"SNR = {snr_db} dB")
    plt.title("科斯塔斯环同步后的频率波动与其噪声带宽的关系")
    plt.xlabel("噪声带宽")
    plt.ylabel("频率波动（归一化）")
    plt.yscale("log")
    plt.grid()
    plt.legend()
    plt.savefig("lock_noise_monotone.png", dpi=300, bbox_inches="tight")

    plt.figure(figsize=(10, 5))
    for i, snr_db in enumerate(snr_db_list):
        plt.plot(loop_bw_list, phase_error_list[i, :], label=f"SNR = {snr_db} dB")
    plt.title("Carrier Synchronization Phase Error")
    plt.xlabel("Frequency Offset (Hz)")
    plt.ylabel("Phase Error (radians)")
    plt.grid()
    plt.legend()
    plt.show()



def lock_time_bpsk():
    bandwidth = 1e6 # Hz
    rolloff_factor = 0.35
    symbol_rate = 2 * bandwidth / (1 + rolloff_factor)
    time_granularity = 1e-9 # seconds
    num_symbols = 100 # Number of symbols to simulate
    filter_symbols = 7 # for RRC filter

    message = np.random.randint(0, 2, num_symbols) * 2 - 1
    message = np.array(message)
    # message = np.random.randint(0, 2, num_symbols)
    # message = 2 * message - 1 # Convert to bipolar

    t, baseband_signal = generate_baseband(message, num_symbols, symbol_rate, time_granularity, filter_symbols, rolloff_factor)

    # freq_offset = 1e3 # Hz
    freq_offset_list = np.arange(0, 24001, 100) # Hz
    phase_offset = 0 # radians
    snr_db_list = np.arange(15, 41, 5)

    lock_time_list = np.zeros((len(snr_db_list), len(freq_offset_list)))
    for snr_db in snr_db_list:
        noise_std = 1 / (10 ** (snr_db / 20))
        noise_std = noise_std / np.sqrt(2)

        for freq_offset in freq_offset_list:

            phase_offset = -2 * freq_offset / symbol_rate * 2 * np.pi

            received_signal = baseband_signal * np.exp(1j * (2 * np.pi * freq_offset * t + phase_offset))
            received_signal = received_signal + np.random.normal(0, noise_std, len(baseband_signal))
            received_signal = received_signal + 1j * np.random.normal(0, noise_std, len(baseband_signal))

            over_sampling_ratio = 4 # sps, samples per symbol
            sample_offset = 0 # per symbol, from 0 to 1/sps

            sampled_signal = sample_and_filter(received_signal, num_symbols, symbol_rate, time_granularity, sample_offset, over_sampling_ratio, filter_symbols, rolloff_factor)

            clock_synced_signal = sync_clock(sampled_signal, num_symbols, over_sampling_ratio, filter_symbols)
            clock_synced_signal = clock_synced_signal[:-filter_symbols]

            loop_bandwidth = 0.03 # Loop bandwidth (Normalized)
            damping_factor = 0.707 # Damping factor
            VCO_gain = 1 # VCO gain

            init_freq = 0
            freq_synced_signal, phase_error, freq = sync_carrier_bpsk(clock_synced_signal, loop_bandwidth, damping_factor, VCO_gain, init_freq)
            freq = freq * symbol_rate
            # if freq_offset > 23000:
            #     plot_sync_freq(freq_synced_signal, phase_error, freq)
            freq_requirement = 11000 # Hz
            delta_freq = np.abs(freq - freq_offset)
            freq_satisfied = np.nonzero(delta_freq > freq_requirement)[0]
            if len(freq_satisfied) == 0:
                lock_symbol = 0
            else:
                lock_symbol = max(freq_satisfied)
            lock_time = lock_symbol / symbol_rate
            lock_time_list[snr_db_list == snr_db, freq_offset_list == freq_offset] = lock_time

    plt.figure(figsize=(10, 5))
    for i, snr_db in enumerate(snr_db_list):
        plt.plot(freq_offset_list, lock_time_list[i, :] * 1e6, label=f"SNR = {snr_db} dB")
    plt.title("科斯塔斯环的锁定时间与载波频偏的关系（噪声带宽Bn=0.03，残余频偏小于11kHz）")
    plt.xlabel("载波频偏 (Hz)")
    plt.ylabel("锁定时间 (us)")
    plt.legend()
    plt.grid()

    plt.figure(figsize=(10, 5))
    for i, snr_db in enumerate(snr_db_list):
        plt.plot(freq_offset_list, lock_time_list[i, :] * symbol_rate, label=f"SNR = {snr_db} dB")
    plt.title("科斯塔斯环的锁定时间与载波频偏的关系（噪声带宽Bn=0.03，残余频偏小于11kHz）")
    plt.xlabel("载波频偏 (Hz)")
    plt.ylabel("锁定时间 (符号数)")
    plt.legend()
    plt.grid()
    plt.savefig("lock_time_bpsk.png", dpi=300, bbox_inches="tight")
    plt.show()

    natural_freq = 8 * damping_factor * loop_bandwidth / (4 * (damping_factor ** 2) + 1)
    max_freq_offset = damping_factor * natural_freq / 2 * symbol_rate
    print(f"Max frequency offset: {max_freq_offset / 1e3:.3f} kHz")
    max_lock_time = 2 * np.pi / natural_freq / symbol_rate
    print(f"Max lock time: {max_lock_time * 1e6:.3f} us")
    max_lock_symbol = round(max_lock_time * symbol_rate)
    print(f"Max lock symbol: {max_lock_symbol} symbols")

    plt.show()


def sync_clock_test():
    bandwidth = 1e6 # Hz
    rolloff_factor = 0.35
    symbol_rate = 2 * bandwidth / (1 + rolloff_factor)
    time_granularity = 1e-9 # seconds
    num_symbols = 100 # Number of symbols to simulate
    filter_symbols = 7 # for RRC filter

    message = [1, -1] * 50
    message = np.array(message)
    # message = np.random.randint(0, 2, num_symbols)
    # message = 2 * message - 1 # Convert to bipolar

    t, baseband_signal = generate_baseband(message, num_symbols, symbol_rate, time_granularity, filter_symbols, rolloff_factor)

    freq_offset = 0 # Hz
    phase_offset = 0 # radians
    snr_db_list = np.arange(15, 41, 5)
    sample_offset_list = np.arange(0, 1, 0.01)

    clock_offset_mean = np.zeros((len(snr_db_list), len(sample_offset_list)))
    clock_offset_std = np.zeros((len(snr_db_list), len(sample_offset_list)))
    for snr_db in snr_db_list:
        noise_std = 1 / (10 ** (snr_db / 20))
        noise_std = noise_std / np.sqrt(2)

        for sample_offset in sample_offset_list:

            received_signal = baseband_signal * np.exp(1j * (2 * np.pi * freq_offset * t + phase_offset))
            received_signal = received_signal + np.random.normal(0, noise_std, len(baseband_signal))
            received_signal = received_signal + 1j * np.random.normal(0, noise_std, len(baseband_signal))

            over_sampling_ratio = 4 # sps, samples per symbol
            # sample_offset = 0.5 # per symbol, from 0 to 1/sps

            sampled_signal = sample_and_filter(received_signal, num_symbols, symbol_rate, time_granularity, sample_offset, over_sampling_ratio, filter_symbols, rolloff_factor)
            sampled_signal = sampled_signal[filter_symbols * over_sampling_ratio:-(filter_symbols+3) * over_sampling_ratio]

            clock_offset = sync_clock_true(sampled_signal)
            # plt.plot(clock_offset)
            # plt.title("Clock Offset")
            # plt.xlabel("Symbol Index")
            # plt.ylabel("Offset (samples)")
            # plt.grid()
            # plt.legend()
            # plt.savefig("clock_offset.png", dpi=300, bbox_inches="tight")
            # plt.show()
            expected_offset = 4 * (1 - sample_offset)
            delta_offset = clock_offset - expected_offset
            for i in range(len(delta_offset)):
                if delta_offset[i] > 2:
                    delta_offset[i] -= 4
                elif delta_offset[i] < -2:
                    delta_offset[i] += 4
            clock_offset_mean[snr_db_list == snr_db, sample_offset_list == sample_offset] = np.mean(delta_offset[8:])
            clock_offset_std[snr_db_list == snr_db, sample_offset_list == sample_offset] = np.std(delta_offset[8:])

    plt.figure(figsize=(10, 5))
    for i, snr_db in enumerate(snr_db_list):
        plt.plot(sample_offset_list, clock_offset_mean[i, :], label=f"SNR = {snr_db} dB")
    plt.title("符号同步中定时偏差的估计误差的均值")
    plt.xlabel("定时误差（与符号的距离）")
    plt.ylabel("估计误差的均值")
    plt.grid()
    plt.legend()
    plt.savefig("clock_offset_mean.png", dpi=300, bbox_inches="tight")

    plt.figure(figsize=(10, 5))
    for i, snr_db in enumerate(snr_db_list):
        plt.plot(sample_offset_list, clock_offset_std[i, :], label=f"SNR = {snr_db} dB")
    plt.title("符号同步中定时偏差的估计误差的方差")
    plt.xlabel("定时误差（与符号的距离）")
    plt.ylabel("估计误差的方差")
    plt.grid()
    plt.legend()
    plt.savefig("clock_offset_std.png", dpi=300, bbox_inches="tight")


    plt.show()


def sync_clock_test2():
    bandwidth = 1e6 # Hz
    rolloff_factor = 0.35
    symbol_rate = 2 * bandwidth / (1 + rolloff_factor)
    time_granularity = 1e-9 # seconds
    num_symbols = 100 # Number of symbols to simulate
    filter_symbols = 7 # for RRC filter

    message = [1, -1] * 50
    message = np.array(message)
    # message = np.random.randint(0, 2, num_symbols)
    # message = 2 * message - 1 # Convert to bipolar

    t, baseband_signal = generate_baseband(message, num_symbols, symbol_rate, time_granularity, filter_symbols, rolloff_factor)

    freq_offset = 0 # Hz
    phase_offset = 0 # radians
    snr_db_list = np.arange(15, 41, 5)
    sample_offset_list = np.arange(0, 1, 0.01)

    clock_offset_list = np.zeros((len(snr_db_list), len(sample_offset_list), 96))
    for snr_db in snr_db_list:
        noise_std = 1 / (10 ** (snr_db / 20))
        noise_std = noise_std / np.sqrt(2)

        for sample_offset in sample_offset_list:

            received_signal = baseband_signal * np.exp(1j * (2 * np.pi * freq_offset * t + phase_offset))
            received_signal = received_signal + np.random.normal(0, noise_std, len(baseband_signal))
            received_signal = received_signal + 1j * np.random.normal(0, noise_std, len(baseband_signal))

            over_sampling_ratio = 4 # sps, samples per symbol
            # sample_offset = 0.5 # per symbol, from 0 to 1/sps

            sampled_signal = sample_and_filter(received_signal, num_symbols, symbol_rate, time_granularity, sample_offset, over_sampling_ratio, filter_symbols, rolloff_factor)
            sampled_signal = sampled_signal[filter_symbols * over_sampling_ratio:-(filter_symbols+3) * over_sampling_ratio]

            clock_offset = sync_clock_true(sampled_signal)
            # plt.plot(clock_offset)
            # plt.title("Clock Offset")
            # plt.xlabel("Symbol Index")
            # plt.ylabel("Offset (samples)")
            # plt.grid()
            # plt.legend()
            # plt.savefig("clock_offset.png", dpi=300, bbox_inches="tight")
            # plt.show()
            expected_offset = 4 * (1 - sample_offset)
            delta_offset = clock_offset - expected_offset
            for i in range(len(delta_offset)):
                if delta_offset[i] > 2:
                    delta_offset[i] -= 4
                elif delta_offset[i] < -2:
                    delta_offset[i] += 4
            clock_offset_list[snr_db_list == snr_db, sample_offset_list == sample_offset, :] = np.abs(delta_offset)

    clock_offset_mean = np.mean(clock_offset_list, axis=1)
    clock_offset_max = np.max(clock_offset_list, axis=1)

    plt.figure(figsize=(10, 5))
    for i, snr_db in enumerate(snr_db_list):
        plt.plot(np.arange(1, 13), clock_offset_mean[i, :12], label=f"SNR = {snr_db} dB")
    plt.title("符号同步中定时偏差的估计误差的均值")
    plt.xlabel("符号数")
    plt.ylabel("估计误差的均值")
    plt.grid()
    plt.legend()
    plt.savefig("clock_offset_symbol_mean.png", dpi=300, bbox_inches="tight")

    plt.figure(figsize=(10, 5))
    for i, snr_db in enumerate(snr_db_list):
        plt.plot(np.arange(1, 13), clock_offset_max[i, :12], label=f"SNR = {snr_db} dB")
    plt.title("符号同步中定时偏差的估计误差的最大值")
    plt.xlabel("符号数")
    plt.ylabel("估计误差的最大值")
    plt.grid()
    plt.legend()
    plt.savefig("clock_offset_symbol_max.png", dpi=300, bbox_inches="tight")

    plt.show()


def sample_offset_test():
    bandwidth = 1e6 # Hz
    rolloff_factor = 0.35
    symbol_rate = 2 * bandwidth / (1 + rolloff_factor)
    time_granularity = 1e-9 # seconds
    num_symbols = 10 # Number of symbols to simulate
    filter_symbols = 8 # for RRC filter

    message = [-1,1,-1,1,1,-1,-1,1,-1,1]
    message = np.array(message) * 3
    # message = np.random.randint(0, 2, num_symbols)
    # message = 2 * message - 1 # Convert to bipolar

    t, baseband_signal = generate_baseband(message, num_symbols, symbol_rate, time_granularity, filter_symbols, rolloff_factor)

    freq_offset = 0 # Hz
    phase_offset = 0 # radians

    received_signal = baseband_signal * np.exp(1j * (2 * np.pi * freq_offset * t + phase_offset))
    # received_signal = received_signal + np.random.normal(0, noise_std, len(baseband_signal))
    # received_signal = received_signal + 1j * np.random.normal(0, noise_std, len(baseband_signal))

    over_sampling_ratio = 4 # sps, samples per symbol
    sample_offset = 0 # per symbol, from 0 to 1/sps

    filtered_signal = filter(received_signal, symbol_rate, time_granularity, filter_symbols, rolloff_factor)

    plt.plot(t * symbol_rate, filtered_signal.real)
    plt.xlabel('Sample Index')
    plt.ylabel('Amplitude')
    
    plt.grid()
    plt.title('Signal Detection Simulation')

    plt.show()


if __name__ == "__main__":
    # lock_time_monotone()
    # lock_time_bpsk()
    # lock_noise_monotone2()
    # sync_clock_test2()
    sample_offset_test()
