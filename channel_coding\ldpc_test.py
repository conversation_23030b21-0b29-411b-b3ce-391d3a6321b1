#!/usr/bin/env python3
"""
LDPC编解码测试和调试脚本
"""

import numpy as np
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ldpc import *

def test_simple_ldpc():
    """测试简单的LDPC码"""
    print("=== 简单LDPC码测试 ===\n")
    
    # 使用一个非常简单的校验矩阵
    H = np.array([
        [1, 1, 1, 0],
        [1, 0, 0, 1]
    ])
    
    print("校验矩阵 H:")
    print(H)
    print()
    
    encoder = LDPCEncoder(H)
    decoder = LDPCDecoder(H)
    
    params = encoder.get_code_parameters()
    print(f"码参数: n={params['n']}, k={params['k']}, 码率={params['rate']:.3f}")
    
    print("\n生成矩阵 G:")
    print(encoder.G)
    print()
    
    # 测试所有可能的信息位组合
    for i in range(2**params['k']):
        info_bits = np.array([(i >> j) & 1 for j in range(params['k'])])
        
        # 编码
        codeword = encoder.encode(info_bits)
        
        # 验证码字
        syndrome = decoder.check_syndrome(codeword)
        is_valid = np.all(syndrome == 0)
        
        print(f"信息位 {info_bits} -> 码字 {codeword} -> 校验子 {syndrome} (有效: {is_valid})")
        
        if is_valid:
            # 测试无噪声解码
            symbols = bits_to_bpsk(codeword)
            llr = bpsk_to_llr(symbols, 0.001)
            
            decoded_bits, converged, iterations = decoder.decode(llr)
            decoded_info = decoded_bits[:params['k']]
            
            print(f"  解码: {decoded_bits} -> 信息位 {decoded_info} (迭代: {iterations}, 收敛: {converged})")
            
            # 检查解码是否正确
            if np.array_equal(info_bits, decoded_info):
                print("  ✓ 解码正确")
            else:
                print("  ✗ 解码错误")
        print()

def test_manual_decoding():
    """手动测试解码过程"""
    print("=== 手动解码测试 ===\n")
    
    # 使用简单的(6,3)码
    H = np.array([
        [1, 1, 0, 1, 0, 0],
        [0, 1, 1, 0, 1, 0],
        [1, 0, 1, 0, 0, 1]
    ])
    
    encoder = LDPCEncoder(H)
    decoder = LDPCDecoder(H)
    
    # 测试特定的信息位
    info_bits = np.array([1, 0, 1])
    codeword = encoder.encode(info_bits)
    
    print(f"信息位: {info_bits}")
    print(f"码字: {codeword}")
    print(f"校验子: {decoder.check_syndrome(codeword)}")
    print()
    
    # 测试不同的LLR值
    print("测试不同的LLR输入:")
    
    # 1. 完美的LLR（无噪声）
    symbols = bits_to_bpsk(codeword)
    print(f"BPSK符号: {symbols}")
    
    # 正确的LLR符号约定：LLR > 0 表示比特为0，LLR < 0 表示比特为1
    llr_perfect = np.where(codeword == 0, 10.0, -10.0)
    print(f"完美LLR: {llr_perfect}")
    
    decoded_bits, converged, iterations = decoder.decode(llr_perfect)
    print(f"解码结果: {decoded_bits} (迭代: {iterations}, 收敛: {converged})")
    print()
    
    # 2. 测试有噪声的情况
    for snr_db in [10, 5, 2]:
        received_signal, noise_var = awgn_channel(symbols, snr_db)
        llr_noisy = bpsk_to_llr(received_signal, noise_var)
        
        print(f"SNR = {snr_db} dB:")
        print(f"  接收信号: {received_signal}")
        print(f"  LLR: {llr_noisy}")
        
        decoded_bits, converged, iterations = decoder.decode(llr_noisy)
        decoded_info = decoded_bits[:3]
        
        print(f"  解码: {decoded_bits} -> 信息位 {decoded_info}")
        print(f"  正确性: {np.array_equal(info_bits, decoded_info)}")
        print()

def debug_belief_propagation():
    """调试置信传播算法"""
    print("=== 置信传播算法调试 ===\n")
    
    # 使用最简单的(3,1)码
    H = np.array([[1, 1, 1]])
    
    encoder = LDPCEncoder(H)
    decoder = LDPCDecoder(H, max_iterations=10)
    
    print("校验矩阵:")
    print(H)
    print()
    
    # 测试信息位 [1]
    info_bits = np.array([1])
    codeword = encoder.encode(info_bits)
    
    print(f"信息位: {info_bits}")
    print(f"码字: {codeword}")
    print()
    
    # 创建LLR
    symbols = bits_to_bpsk(codeword)
    llr = bpsk_to_llr(symbols, 0.1)
    
    print(f"BPSK符号: {symbols}")
    print(f"LLR: {llr}")
    print()
    
    # 手动执行一步置信传播
    print("手动置信传播:")
    
    # 变量节点到校验节点的连接
    var_to_check = []
    for j in range(3):
        var_to_check.append(np.where(H[:, j] == 1)[0])
    
    print(f"变量节点连接: {var_to_check}")
    
    # 校验节点到变量节点的连接
    check_to_var = []
    for i in range(1):
        check_to_var.append(np.where(H[i, :] == 1)[0])
    
    print(f"校验节点连接: {check_to_var}")
    print()
    
    # 解码
    decoded_bits, converged, iterations = decoder.decode(llr, verbose=True)
    print(f"最终解码: {decoded_bits}")

def main():
    """主函数"""
    print("LDPC编解码测试和调试")
    print("=" * 50)
    print()
    
    test_simple_ldpc()
    print("=" * 50 + "\n")
    
    test_manual_decoding()
    print("=" * 50 + "\n")
    
    debug_belief_propagation()

if __name__ == "__main__":
    main()
